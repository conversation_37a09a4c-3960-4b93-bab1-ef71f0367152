"use client";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Trophy, Save, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useAuthStore } from "@/store/authStore";
import { toast } from "sonner";
import { apiService } from "@/services/api";

export default function CreateCompetition() {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    maxTeams: 32,
    entryFee: 0,
    prizeFirst: 0,
    prizeSecond: 0,
    prizeThird: 0,
    rules: "",
    isPublic: true,
    allowSpectators: true,
    maxGamesPerTeam: 10
  });

  // Redirect if not authenticated (admin controls are now accessible to all users)
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/");
    }
  }, [isAuthenticated, navigate]);

  if (!isAuthenticated) {
    return null;
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name.trim()) {
      toast.error("Competition name is required");
      return;
    }
    
    if (!formData.startDate || !formData.endDate) {
      toast.error("Start and end dates are required");
      return;
    }
    
    if (new Date(formData.startDate) >= new Date(formData.endDate)) {
      toast.error("End date must be after start date");
      return;
    }

    try {
      setIsLoading(true);

      // Convert form data to match API expectations
      const competitionData = {
        name: formData.name,
        description: formData.description,
        startDate: formData.startDate,
        endDate: formData.endDate,
        maxTeams: formData.maxTeams,
        entryFee: formData.entryFee,
        prizeFirst: formData.prizeFirst.toString(),
        prizeSecond: formData.prizeSecond.toString(),
        prizeThird: formData.prizeThird.toString(),
        rules: formData.rules,
        isPublic: formData.isPublic,
        allowSpectators: formData.allowSpectators
      };

      await apiService.createCompetition(competitionData);

      toast.success("Competition created successfully!");
      navigate("/admin/competitions");
    } catch (error) {
      console.error("Error creating competition:", error);
      toast.error("Failed to create competition");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate("/admin/competitions")}
            className="text-[#E1C760] hover:bg-[#E1C760]/10"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            <Trophy className="h-6 w-6 text-[#E1C760]" />
            <h1 className="text-2xl font-bold text-[#E1C760]">Create Competition</h1>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760]">Basic Information</CardTitle>
              <CardDescription className="text-gray-400">
                Set up the basic details for your competition
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-white mb-2 block">Competition Name *</Label>
                  <Input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    placeholder="Spring Championship 2024"
                    required
                  />
                </div>
                <div>
                  <Label className="text-white mb-2 block">Max Teams</Label>
                  <Input
                    type="number"
                    name="maxTeams"
                    value={formData.maxTeams}
                    onChange={handleInputChange}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="2"
                    max="128"
                  />
                </div>
              </div>
              
              <div>
                <Label className="text-white mb-2 block">Description</Label>
                <Textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="bg-transparent border-[#E1C760]/30 text-white"
                  placeholder="Describe your competition..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-white mb-2 block">Start Date *</Label>
                  <Input
                    type="datetime-local"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleInputChange}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    required
                  />
                </div>
                <div>
                  <Label className="text-white mb-2 block">End Date *</Label>
                  <Input
                    type="datetime-local"
                    name="endDate"
                    value={formData.endDate}
                    onChange={handleInputChange}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Prize Structure */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760]">Prize Structure</CardTitle>
              <CardDescription className="text-gray-400">
                Configure entry fees and prize distribution
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <Label className="text-white mb-2 block">Entry Fee ($)</Label>
                  <Input
                    type="number"
                    name="entryFee"
                    value={formData.entryFee}
                    onChange={handleInputChange}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <Label className="text-white mb-2 block">1st Place ($)</Label>
                  <Input
                    type="number"
                    name="prizeFirst"
                    value={formData.prizeFirst}
                    onChange={handleInputChange}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <Label className="text-white mb-2 block">2nd Place ($)</Label>
                  <Input
                    type="number"
                    name="prizeSecond"
                    value={formData.prizeSecond}
                    onChange={handleInputChange}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="0"
                    step="0.01"
                  />
                </div>
                <div>
                  <Label className="text-white mb-2 block">3rd Place ($)</Label>
                  <Input
                    type="number"
                    name="prizeThird"
                    value={formData.prizeThird}
                    onChange={handleInputChange}
                    className="bg-transparent border-[#E1C760]/30 text-white"
                    min="0"
                    step="0.01"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Settings */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760]">Competition Settings</CardTitle>
              <CardDescription className="text-gray-400">
                Configure competition rules and settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-white mb-2 block">Max Games Per Team</Label>
                <Input
                  type="number"
                  name="maxGamesPerTeam"
                  value={formData.maxGamesPerTeam}
                  onChange={handleInputChange}
                  className="bg-transparent border-[#E1C760]/30 text-white"
                  min="1"
                  max="50"
                />
              </div>

              <div>
                <Label className="text-white mb-2 block">Rules</Label>
                <Textarea
                  name="rules"
                  value={formData.rules}
                  onChange={handleInputChange}
                  className="bg-transparent border-[#E1C760]/30 text-white"
                  placeholder="Competition rules and regulations..."
                  rows={4}
                />
              </div>

              <div className="flex gap-6">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isPublic"
                    checked={formData.isPublic}
                    onCheckedChange={(checked) => handleCheckboxChange('isPublic', checked as boolean)}
                  />
                  <Label htmlFor="isPublic" className="text-white">Public Competition</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="allowSpectators"
                    checked={formData.allowSpectators}
                    onCheckedChange={(checked) => handleCheckboxChange('allowSpectators', checked as boolean)}
                  />
                  <Label htmlFor="allowSpectators" className="text-white">Allow Spectators</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate("/admin/competitions")}
              className="border-gray-500/30 text-gray-400 hover:bg-gray-500/10"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80 disabled:opacity-50"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Create Competition
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
