"use client";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ArrowLeft, User, Save, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { useAuthStore } from "@/store/authStore";
import { toast } from "sonner";
import { apiService } from "@/services/api";

interface UserData {
  id: string;
  username: string;
  email: string;
  isVerified: boolean;
  isActive: boolean;
  isAdmin: boolean;
  lastLoginAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function EditUser() {
  const navigate = useNavigate();
  const { userId } = useParams<{ userId: string }>();
  const { user, isAuthenticated } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Form state
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    isVerified: false,
    isActive: true,
    isAdmin: false
  });

  // Redirect if not authenticated (admin controls are now accessible to all users)
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/");
    }
  }, [isAuthenticated, navigate]);

  // Load user data
  useEffect(() => {
    if (userId) {
      loadUser();
    }
  }, [userId]);

  if (!isAuthenticated) {
    return null;
  }

  const loadUser = async () => {
    try {
      setIsLoadingData(true);
      const userData = await apiService.getUserById(userId!);
      
      setFormData({
        username: userData.username,
        email: userData.email,
        isVerified: userData.isVerified,
        isActive: userData.isActive,
        isAdmin: userData.isAdmin
      });
    } catch (error) {
      console.error("Error loading user:", error);
      toast.error("Failed to load user data");
      navigate("/admin/users");
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.username.trim()) {
      toast.error("Username is required");
      return;
    }
    
    if (!formData.email.trim()) {
      toast.error("Email is required");
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    try {
      setIsLoading(true);

      // Prepare update data
      const updateData = {
        username: formData.username,
        email: formData.email,
        isVerified: formData.isVerified,
        isActive: formData.isActive,
        isAdmin: formData.isAdmin
      };

      await apiService.updateUser(userId!, updateData);

      toast.success("User updated successfully!");
      navigate("/admin/users");
    } catch (error) {
      console.error("Error updating user:", error);
      toast.error("Failed to update user");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin text-[#E1C760]" />
          <span className="text-[#E1C760]">Loading user data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate("/admin/users")}
            className="text-[#E1C760] hover:bg-[#E1C760]/10"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            <User className="h-6 w-6 text-[#E1C760]" />
            <h1 className="text-2xl font-bold text-[#E1C760]">Edit User</h1>
          </div>
        </div>

        {/* Edit Form */}
        <Card className="bg-black/50 border-[#E1C760]/30">
          <CardHeader>
            <CardTitle className="text-[#E1C760]">User Information</CardTitle>
            <CardDescription className="text-gray-400">
              Update user details and permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="username" className="text-white">Username</Label>
                  <Input
                    id="username"
                    type="text"
                    value={formData.username}
                    onChange={(e) => handleInputChange("username", e.target.value)}
                    className="bg-black/50 border-[#E1C760]/30 text-white"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-white">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="bg-black/50 border-[#E1C760]/30 text-white"
                    required
                  />
                </div>
              </div>

              {/* User Status */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-[#E1C760]">User Status</h3>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isVerified"
                    checked={formData.isVerified}
                    onCheckedChange={(checked) => handleInputChange("isVerified", checked as boolean)}
                    className="border-[#E1C760]/30"
                  />
                  <Label htmlFor="isVerified" className="text-white">Email Verified</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleInputChange("isActive", checked as boolean)}
                    className="border-[#E1C760]/30"
                  />
                  <Label htmlFor="isActive" className="text-white">Account Active</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isAdmin"
                    checked={formData.isAdmin}
                    onCheckedChange={(checked) => handleInputChange("isAdmin", checked as boolean)}
                    className="border-[#E1C760]/30"
                  />
                  <Label htmlFor="isAdmin" className="text-white">Administrator</Label>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex gap-4 pt-6">
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="bg-[#E1C760] text-black hover:bg-[#E1C760]/90 flex items-center gap-2"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  {isLoading ? "Updating..." : "Update User"}
                </Button>
                
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate("/admin/users")}
                  className="border-[#E1C760]/30 text-[#E1C760] hover:bg-[#E1C760]/10"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
