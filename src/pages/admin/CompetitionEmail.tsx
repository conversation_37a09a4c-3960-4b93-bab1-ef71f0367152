"use client";
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ArrowLeft, Mail, Send, Loader2, Users } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthStore } from "@/store/authStore";
import { apiService } from "@/services/api";
import { toast } from "sonner";

export default function CompetitionEmail() {
  const navigate = useNavigate();
  const { competitionId } = useParams<{ competitionId: string }>();
  const { user, isAuthenticated } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [competitionName, setCompetitionName] = useState("");
  const [teamCount, setTeamCount] = useState(0);

  // Form state
  const [formData, setFormData] = useState({
    subject: "",
    message: ""
  });

  // Redirect if not authenticated (admin controls are now accessible to all users)
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/");
    }
  }, [isAuthenticated, navigate]);

  // Load competition data
  useEffect(() => {
    if (competitionId) {
      loadCompetitionData();
    }
  }, [competitionId]);

  if (!isAuthenticated) {
    return null;
  }

  const loadCompetitionData = async () => {
    try {
      setIsLoadingData(true);
      const [competition, teams] = await Promise.all([
        apiService.getCompetitionById(competitionId!),
        apiService.getCompetitionTeams(competitionId!)
      ]);
      
      setCompetitionName(competition.name);
      setTeamCount(teams.length);
      
      // Set default subject
      setFormData(prev => ({
        ...prev,
        subject: `Update: ${competition.name}`
      }));
    } catch (error) {
      console.error("Error loading competition data:", error);
      toast.error("Failed to load competition data");
      navigate("/admin/competitions");
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.subject.trim()) {
      toast.error("Email subject is required");
      return;
    }
    
    if (!formData.message.trim()) {
      toast.error("Email message is required");
      return;
    }

    try {
      setIsLoading(true);
      
      await apiService.sendCompetitionEmail(competitionId!, {
        subject: formData.subject,
        message: formData.message
      });
      
      toast.success("Emails sent successfully to all competition players!");
      navigate(`/admin/competitions/${competitionId}`);
    } catch (error) {
      console.error("Error sending emails:", error);
      toast.error("Failed to send emails");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <Loader2 className="h-8 w-8 text-[#E1C760] animate-spin" />
      </div>
    );
  }

  const playerCount = teamCount * 2; // Each team has 2 players

  return (
    <div className="min-h-screen bg-black text-white p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(`/admin/competitions/${competitionId}`)}
            className="text-[#E1C760] hover:bg-[#E1C760]/10"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex items-center gap-2">
            <Mail className="h-6 w-6 text-[#E1C760]" />
            <div>
              <h1 className="text-2xl font-bold text-[#E1C760]">Send Competition Email</h1>
              {competitionName && (
                <p className="text-gray-400">{competitionName}</p>
              )}
            </div>
          </div>
        </div>

        {/* Recipients Info */}
        <Card className="bg-black/50 border-[#E1C760]/30 mb-6">
          <CardHeader>
            <CardTitle className="text-[#E1C760] flex items-center gap-2">
              <Users className="h-5 w-5" />
              Email Recipients
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-[#E1C760]">{teamCount}</div>
                <div className="text-gray-400">Teams</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-400">{playerCount}</div>
                <div className="text-gray-400">Players</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-400">{playerCount}</div>
                <div className="text-gray-400">Email Recipients</div>
              </div>
            </div>
            <p className="text-gray-500 text-sm mt-4 text-center">
              This email will be sent to all players registered in this competition
            </p>
          </CardContent>
        </Card>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email Content */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760]">Email Content</CardTitle>
              <CardDescription className="text-gray-400">
                Compose your message to all competition participants
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-white mb-2 block">Subject *</Label>
                <Input
                  type="text"
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  className="bg-transparent border-[#E1C760]/30 text-white"
                  placeholder="Competition Update"
                  required
                />
              </div>
              
              <div>
                <Label className="text-white mb-2 block">Message *</Label>
                <Textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  className="bg-transparent border-[#E1C760]/30 text-white"
                  placeholder="Dear participants,&#10;&#10;We hope you're enjoying the competition! Here's an important update...&#10;&#10;Best regards,&#10;The Thunee Team"
                  rows={12}
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Tip: Be clear and concise. Include any important dates, rule changes, or announcements.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Email Preview */}
          <Card className="bg-black/50 border-[#E1C760]/30">
            <CardHeader>
              <CardTitle className="text-[#E1C760]">Email Preview</CardTitle>
              <CardDescription className="text-gray-400">
                Preview how your email will appear to recipients
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-white text-black p-6 rounded-lg">
                <div className="border-b border-gray-200 pb-4 mb-4">
                  <div className="text-sm text-gray-600 mb-2">
                    <strong>From:</strong> Thunee Admin &lt;<EMAIL>&gt;
                  </div>
                  <div className="text-sm text-gray-600 mb-2">
                    <strong>To:</strong> Competition Participants
                  </div>
                  <div className="text-lg font-semibold">
                    <strong>Subject:</strong> {formData.subject || "Competition Update"}
                  </div>
                </div>
                <div className="whitespace-pre-wrap">
                  {formData.message || "Your message will appear here..."}
                </div>
                <div className="mt-6 pt-4 border-t border-gray-200 text-sm text-gray-600">
                  <p>This email was sent to all participants of: <strong>{competitionName}</strong></p>
                  <p>If you have any questions, please contact the competition administrators.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate(`/admin/competitions/${competitionId}`)}
              className="border-gray-500/30 text-gray-400 hover:bg-gray-500/10"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !formData.subject.trim() || !formData.message.trim()}
              className="bg-[#E1C760] text-black hover:bg-[#E1C760]/80 disabled:opacity-50"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Send Email to {playerCount} Players
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
